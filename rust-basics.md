# Rust basics

## Basics of starting and running a Rust project

### Creating a new project

```bash
cargo new my_rust_project
```

### What is Cargo?

Cargo is Rust's build system and package manager, among other things. It's responsible for building your code, downloading the libraries your code depends on, and building those libraries.  
Similar to npm in JavaScript or pip in Python.

### Running a project

```bash
cargo run
```

### Building a project

```bash
cargo build
```


## Basic Beginner Syntax

### Main function and Macros

What a function is should be a familiar concept already. The main function is the entry point of a Rust program. It is denoted by the `fn` (function) keyword, followed by the name of the function. In this case, `main`.

```rust
fn main() {
    println!("Hello, world!");
}
``` 

Another often used thing in Rust are "macros". Macros are a way to write code that writes code. They are denoted by a `!` after the name. A common example is the `println!` macro. Macros can be thought about as essentially a built in function.

### Comments

Comments are denoted by `//` for single line comments, and `/* */` for multi-line comments.

### Errors

Rust is a compiled language, which means that it is compiled before it is run. This means that errors are caught before the program is run. This is a good thing, as it means that you can fix errors before you run your program. 

When an error is encountered, the compiler will print out an error message. The error message will tell you what the error is, and where it is located. If you are ever confused on an error or the fix, you can run:

```bash
rustc --explain E0001
```

Where `E0001` is the error code. This will print out a description of the error, and possible fixes.

### Primitives (Basic Data Types)

Rust has several fundamental data types called primitives. Think of these as the basic building blocks for all data in your programs. They come in four main categories:

- Scalar Types (single values):
    - Integers (whole numbers)
    - Floating-point numbers (decimal numbers)
    - Booleans (true/false)
    - Characters (single letters, symbols, or emoji)
- Compound Types (grouping of values):
    - Tuples (fixed-size collections of values)
    - Arrays (fixed-size collections of values)
    - Slices (views into arrays or other collections)

Let's focus on numbers first:

#### Integer Types
Integers are whole numbers without decimal points. In Rust, they come in two flavors:
- Signed integers (can be positive or negative)
    - Examples: `i8`, `i16`, `i32`, `i64`, `i128`, `isize`
- Unsigned integers (positive numbers only)
    - Examples: `u8`, `u16`, `u32`, `u64`, `u128`, `usize`

The number in the type name tells you how many bits it uses. For example:
```rust
let small_number: i8 = -128;  // Range: -128 to 127
let bigger_number: i32 = 2_000_000;  // Much larger range
let positive_only: u8 = 255;  // Range: 0 to 255
```

Note: If you don't specify a type, Rust will usually default to `i32`, which is good for most numbers.

#### Floating-Point Types
For numbers with decimal points, Rust has two main types:
```rust
let precise: f64 = 3.14159265359;  // 64 bits, more precise (default)
let less_precise: f32 = 3.14;      // 32 bits, less precise but uses less memory
```

These types are always signed (can be positive or negative). Most of the time, you'll want to use `f64` as it's the default and provides good precision.

Example of using these types:
```rust
fn main() {
    let integer: i32 = 42;
    let float: f64 = 3.14;
    let sum = integer as f64 + float;  // Converting integer to float
    println!("Sum is: {}", sum);  // Prints: Sum is: 45.14
}
```

#### Boolean

Booleans are a type that can only be `true` or `false`. They are denoted by the `bool` keyword. They are 1 byte in size.

#### Characters

Characters are a type that can only be a single letter, number, or symbol. They are denoted by the `char` keyword. They are 4 bytes in size.

#### Tuples

Tuples are a type that can store multiple values of ANY type. They are denoted by the `()` keyword. They are 4 bytes in size. They can store a maximum of 12 values. Tuples use dot notation to access values.

```rust
let tuple: (i32, f64, u8) = (500, 6.4, 1);
println!("The value of tuple is: {}", tuple.0);  // Prints: The value of tuple is: 500
```

#### Arrays

Arrays are a type that can hold multiple similar values. They are denoted by the `[]` keyword. They are 4 bytes in size. They can store a maximum of 32 values. Arrays use bracket notation to access values.

```rust
let array: [i32; 5] = [1, 2, 3, 4, 5];
println!("The value of array is: {}", array[0]);  // Prints: The value of array is: 1
```

#### Slices

Slices allow for access ti a subset of elements in a collection. 

```rust
let array: [i32; 5] = [1, 2, 3, 4, 5];
let slice: &[i32] = &array[1..3];
println!("The value of slice is: {:?}", slice);  // Prints: The value of slice is: [2, 3]

let mut array2: [i32; 5] = [1, 2, 3, 4, 5];
let slice: &[i32] = &mut array2[1..3];
slice[0] = 10;
println!("The value of array2 is: {:?}", array2);  // Prints: The value of array2 is: [1, 10, 3, 4, 5]
```

### Variables

Variables are a way to store data in Rust. They are denoted by the `let` keyword. Variables are immutable by default, meaning that once you assign a value to a variable, you cannot change it. If you want to be able to change a variable, you can use the `mut` keyword.

```rust
let x = 5;  // Immutable variable (cannot change value)
let mut y = 10;  // Mutable variable (can change value)
```

#### Constants

Constants are similar to variables, but they are immutable and their value must be known at compile time. They are denoted by the `const` keyword, must be declaed in scream case, and must have a type annotation. Constants are useful for values that you know will not change, such as mathematical constants or configuration values.

Constants can also be declared in global scope, outside of any function.

```rust
const PI: f64 = 3.14159265359;  // Mathematical constant
const MAX_CONNECTIONS: u32 = 1000;  // Configuration value
```

### Scope and Shadowing

Scope is the area of a program in which an item is valid. A variable can only be used if it is in scope. An easy example of this is:

```rust
fn main() {
    let x = 5;
    {
        let y = 10;
        println!("The value of x is: {}", x);  // Valid
        println!("The value of y is: {}", y);  // Valid
    }
    println!("The value of x is: {}", x);  // Valid
    println!("The value of y is: {}", y);  // Invalid, y is out of scope
}
```

Shadowing is when you declare a new variable with the same name as a previous variable. The new variable shadows the previous variable. The previous variable is still valid, but it is no longer in scope.

```rust
fn main() {
    let x = 5;
    let x = 10;
    println!("The value of x is: {}", x);  // Prints 10 but will also print a warning
}
```

### Suffixes and Underscores

Suffixes are used to denote the type of a numeric literal. 

```rust
let x = 5i32;  // 5 is an i32
let y = 10u8;  // 10 is an u8
```

Underscores can be used to make large numbers more readable. They are ignored by the compiler.

```rust
let x = 1_000_000;  // 1 million
let y = 40_u64;  // 40
```

